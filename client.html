<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>约会规划API客户端</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1, h2 {
            color: #333;
        }
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .panel {
            flex: 1;
            min-width: 300px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px;
            margin-bottom: 20px;
        }
        .panel h2 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .output {
            margin-top: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .status {
            color: #777;
            font-style: italic;
            margin-top: 10px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #45a049;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .hidden {
            display: none;
        }
        .category {
            font-weight: bold;
            margin-top: 10px;
            color: #0066cc;
        }
        .item {
            margin-left: 15px;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>约会规划API客户端</h1>
    <p>这个客户端演示了如何连接和使用约会规划API的SSE端点</p>
    
    <div class="container">
        <div class="panel">
            <h2>解释用户约会期望</h2>
            <button id="explainBtn">获取约会期望解释</button>
            <div class="status" id="explainStatus"></div>
            <div class="loading hidden" id="explainLoading"></div>
            <div class="output" id="explainOutput"></div>
        </div>
        
        <div class="panel">
            <h2>用户需求理解和任务拆分</h2>
            <button id="thinkPlanBtn">获取需求理解与任务拆分</button>
            <div class="status" id="thinkPlanStatus"></div>
            <div class="loading hidden" id="thinkPlanLoading"></div>
            <div class="output" id="thinkPlanOutput"></div>
        </div>
    </div>
    
    <div class="container">
        <div class="panel">
            <h2>查询目的地信息</h2>
            <button id="searchLocationBtn">获取目的地信息</button>
            <div class="status" id="searchLocationStatus"></div>
            <div class="loading hidden" id="searchLocationLoading"></div>
            <div class="output" id="searchLocationOutput"></div>
        </div>
        
        <div class="panel">
            <h2>获取详细约会计划</h2>
            <button id="detailedPlanBtn">获取详细约会计划</button>
            <div class="status" id="detailedPlanStatus"></div>
            <div class="loading hidden" id="detailedPlanLoading"></div>
            <div class="output" id="detailedPlanOutput"></div>
        </div>
    </div>

    <script>
        // 示例请求数据
        const requestData = {
            user_input: "这周六下午我想和女朋友在COCO Park约会",
            request_type: "dating",
            dating_params: {
                to_location_province: "广东省",
                to_location_city: "深圳市",
                to_location_district: "福田区",
                to_location_name: "COCO Park",
                
                user_lat_lng: "113.999706,22.588863",
                user_location_province: "广东省",
                user_location_city: "深圳市",
                user_location_district: "南山区",
                user_location_name: "塘朗城广场",
                
                departure_lat_lng: "113.999706,22.588863",
                departure_province: "广东省",
                departure_city: "深圳市",
                departure_district: "南山区",
                departure_name: "塘朗城广场",
                
                comp_lat_lng: "114.054007,22.533569",
                comp_location_province: "广东省",
                comp_location_city: "深圳市",
                comp_location_district: "宝安区",
                comp_location_name: "尚都花园",
                
                male_last_name: "张",
                female_last_name: "李",
                
                trans_tool_user: "bus",
                trans_tool_comp: "bus",
                
                date: "2025-05-24",
                time_start: "14:00",
                time_end: "17:00",
                
                dating_times: 1,
                dating_type: "浪漫型",
                more_thoughts: "我希望约会时可以吃到冰淇淋"
            }
        };

        const API_BASE_URL = 'http://localhost:8000';
        
        // 辅助函数：创建SSE连接
        function connectToSSE(endpoint, outputElement, statusElement, loadingElement, processEventCallback = null) {
            // 重置输出
            outputElement.textContent = '';
            statusElement.textContent = '正在连接...';
            loadingElement.classList.remove('hidden');
            
            // 创建请求选项
            const requestOptions = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            };
            
            // 使用fetch进行请求
            fetch(`${API_BASE_URL}${endpoint}`, requestOptions)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP 错误! 状态: ${response.status}`);
                    }
                    
                    // 创建一个新的Reader来处理响应流
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    
                    statusElement.textContent = '已连接，接收数据中...';
                    
                    // 处理响应流
                    function processStream() {
                        return reader.read().then(({ done, value }) => {
                            if (done) {
                                statusElement.textContent = '数据接收完成';
                                loadingElement.classList.add('hidden');
                                return;
                            }
                            
                            // 解码接收到的数据
                            const text = decoder.decode(value, { stream: true });
                            
                            // 添加调试输出
                            console.log('收到SSE数据:', text);
                            
                            // 处理SSE格式的数据
                            const events = text.split('\n\n');
                            for (const event of events) {
                                if (!event.trim()) continue;
                                
                                const lines = event.split('\n');
                                let eventType = 'message';
                                let data = '';
                                
                                for (const line of lines) {
                                    if (line.startsWith('event:')) {
                                        eventType = line.substring(6).trim();
                                    } else if (line.startsWith('data:')) {
                                        data = line.substring(5).trim();
                                    }
                                }
                                
                                // 输出调试信息
                                console.log(`事件类型: ${eventType}, 数据: ${data}`);
                                
                                if (data) {
                                    if (processEventCallback) {
                                        processEventCallback(eventType, data, outputElement);
                                    } else {
                                        // 默认处理
                                        outputElement.textContent += data + '\n';
                                    }
                                }
                            }
                            
                            // 继续处理流
                            return processStream();
                        });
                    }
                    
                    return processStream();
                })
                .catch(error => {
                    statusElement.textContent = `错误: ${error.message}`;
                    loadingElement.classList.add('hidden');
                    console.error('请求错误:', error);
                });
        }
        
        // 初始化所有按钮
        document.addEventListener('DOMContentLoaded', () => {
            // 解释用户约会期望
            document.getElementById('explainBtn').addEventListener('click', () => {
                const outputElement = document.getElementById('explainOutput');
                const statusElement = document.getElementById('explainStatus');
                const loadingElement = document.getElementById('explainLoading');
                
                connectToSSE('/explain/stream', outputElement, statusElement, loadingElement, 
                    (eventType, data, outputElement) => {
                        console.log(`处理约会期望事件: ${eventType}, 数据: ${data}`);
                        
                        if (eventType === 'start') {
                            outputElement.textContent = '开始处理...';
                        } else if (eventType === 'result') {
                            // 清除之前的内容，只保留结果
                            outputElement.innerHTML = `<div><strong>约会期望解释:</strong></div><div>${data}</div>`;
                        } else if (eventType === 'end') {
                            outputElement.innerHTML += '<div><em>处理完成！</em></div>';
                        } else {
                            // 其他事件类型
                            outputElement.innerHTML += `<div>${data}</div>`;
                        }
                    });
            });
            
            // 用户需求理解和任务拆分
            document.getElementById('thinkPlanBtn').addEventListener('click', () => {
                const outputElement = document.getElementById('thinkPlanOutput');
                const statusElement = document.getElementById('thinkPlanStatus');
                const loadingElement = document.getElementById('thinkPlanLoading');
                
                connectToSSE('/think-plan/stream', outputElement, statusElement, loadingElement);
            });
            
            // 查询目的地信息
            document.getElementById('searchLocationBtn').addEventListener('click', () => {
                const outputElement = document.getElementById('searchLocationOutput');
                const statusElement = document.getElementById('searchLocationStatus');
                const loadingElement = document.getElementById('searchLocationLoading');
                
                connectToSSE('/search-location/stream', outputElement, statusElement, loadingElement,
                    (eventType, data, outputElement) => {
                        console.log(`处理目的地信息事件: ${eventType}, 数据: ${data}`);
                        
                        if (eventType === 'start') {
                            outputElement.innerHTML = '<div>开始获取目的地信息...</div>';
                        } else if (eventType === 'info') {
                            outputElement.innerHTML += `<div>${data}</div>`;
                        } else if (eventType === 'food') {
                            if (!outputElement.querySelector('.food-category')) {
                                const category = document.createElement('div');
                                category.className = 'category food-category';
                                category.textContent = '餐饮场所:';
                                outputElement.appendChild(category);
                            }
                            // 直接使用服务器发送的数据
                            const item = document.createElement('div');
                            item.className = 'item';
                            item.textContent = data;
                            outputElement.appendChild(item);
                        } else if (eventType === 'shopping') {
                            if (!outputElement.querySelector('.shopping-category')) {
                                const category = document.createElement('div');
                                category.className = 'category shopping-category';
                                category.textContent = '购物场所:';
                                outputElement.appendChild(category);
                            }
                            // 直接使用服务器发送的数据
                            const item = document.createElement('div');
                            item.className = 'item';
                            item.textContent = data;
                            outputElement.appendChild(item);
                        } else if (eventType === 'entertainment') {
                            if (!outputElement.querySelector('.entertainment-category')) {
                                const category = document.createElement('div');
                                category.className = 'category entertainment-category';
                                category.textContent = '娱乐场所:';
                                outputElement.appendChild(category);
                            }
                            // 直接使用服务器发送的数据
                            const item = document.createElement('div');
                            item.className = 'item';
                            item.textContent = data;
                            outputElement.appendChild(item);
                        } else if (eventType === 'sightseeing') {
                            if (!outputElement.querySelector('.sightseeing-category')) {
                                const category = document.createElement('div');
                                category.className = 'category sightseeing-category';
                                category.textContent = '景点:';
                                outputElement.appendChild(category);
                            }
                            // 直接使用服务器发送的数据
                            const item = document.createElement('div');
                            item.className = 'item';
                            item.textContent = data;
                            outputElement.appendChild(item);
                        } else if (eventType === 'summary' || eventType === 'end') {
                            outputElement.textContent += '\n' + data;
                        } else {
                            outputElement.textContent += data + '\n';
                        }
                    });
            });
            
            // 获取详细约会计划
            document.getElementById('detailedPlanBtn').addEventListener('click', () => {
                const outputElement = document.getElementById('detailedPlanOutput');
                const statusElement = document.getElementById('detailedPlanStatus');
                const loadingElement = document.getElementById('detailedPlanLoading');
                
                connectToSSE('/detailed-plan/stream', outputElement, statusElement, loadingElement);
            });
        });
    </script>
</body>
</html>
