#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File	:embedding.py
@Time	:2025/05/14 10:54:02
<AUTHOR>
@Mail	:<EMAIL>
'''

import os
import numpy as np
import asyncio
from openai import AsyncOpenAI
from typing import List, Tuple, Optional



class TextEmbeddingService:
    """
    文本向量嵌入服务
    """

    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(TextEmbeddingService, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self, api_key=None, model="text-embedding-v3", dimensions=1024):
        if self._initialized:
            return

        self.api_key = api_key or os.environ.get("DASH_SCOPE_API_KEY")
        if not self.api_key:
            raise ValueError("DASHSCOPE_API_KEY 环境变量未设置或未提供API Key")

        self.model = model
        self.dimensions = dimensions
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        self.client = AsyncOpenAI(api_key=self.api_key, base_url=self.base_url)
        self._initialized = True

    @staticmethod
    def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
        """
        计算两个向量的余弦相似度

        Args:
            vec1: 第一个向量
            vec2: 第二个向量

        Returns:
            float: 余弦相似度值，范围在[-1, 1]
        """
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        return dot_product / (norm1 * norm2)

    async def get_embedding(
        self, text: str, model: str = None, dimensions: int = None
    ) -> Tuple[Optional[List[float]], str]:
        """
        异步获取文本的向量表示

        Args:
            text: 要向量化的文本
            model: 模型名称，如果为None则使用默认模型
            dimensions: 向量维度，如果为None则使用默认维度

        Returns:
            Tuple[List[float], str]: (文本的向量表示, 原始文本)
        """
        model = model or self.model
        dimensions = dimensions or self.dimensions

        try:
            response = await self.client.embeddings.create(
                model=model, input=text, dimensions=dimensions, encoding_format="float"
            )

            # 返回embedding向量
            return response.data[0].embedding, text
        except Exception as e:
            print(f"获取embedding失败: {e}")
            return None, text

    def get_embedding_sync(
        self, text: str, model: str = None, dimensions: int = None
    ) -> Optional[List[float]]:
        """
        同步获取文本向量表示

        Args:
            text: 要向量化的文本
            model: 模型名称
            dimensions: 向量维度

        Returns:
            List[float]: 文本的向量表示
        """
        return asyncio.run(self.get_embedding(text, model, dimensions))[0]
