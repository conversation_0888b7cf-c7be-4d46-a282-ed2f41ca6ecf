#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File	:location_helper.py
@Time	:2025/05/16 10:16:14
<AUTHOR>
@Mail	:<EMAIL>
'''


import os
import aiohttp
import asyncio
from const import TODAY, WEEK<PERSON><PERSON>
from logger import LOGGER
from prompt import EXTRACT_TO_LOCATION_PROMPT
from llm import async_client


class LocationHelper(object):
    
    def __init__(self):
        self.amap_api_key = os.environ.get('alimap_key')
        self.lat_lng2city_url = 'https://restapi.amap.com/v3/geocode/regeo'
        self.keyword_search_url = 'https://restapi.amap.com/v3/place/text'
        self.nearby_search_url = 'https://restapi.amap.com/v3/place/around'
        self.keyword_search_url = 'https://restapi.amap.com/v3/place/text'
        self.bus_route_plan_url = 'https://restapi.amap.com/v3/direction/transit/integrated'
        self.car_route_plan_url = 'https://restapi.amap.com/v3/direction/driving'

        # self.poi_types = '050000|060100|110000|080102|080103|080109|080112|080114|080115|080116|080117|080118|080200|080302|080500|080501|080503|080504|080505|'


    async def lat_lng2city(self, lat_lng: str):
        """
        逆地理编码-坐标转地址-将经纬度坐标转换成城市名称
        Args:
            lat_lng (str): 经纬度坐标
        Returns:
            dict: 包含地理编码信息的字典
        """
        params = {
            'key': self.amap_api_key,
            'location': lat_lng, # '113.999706,22.588863' (塘朗城广场)
            'output': 'json'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(self.lat_lng2city_url, params=params) as response:
                response.raise_for_status()
                result = await response.json()
                LOGGER.info(result)

                province = result['regeocode']['addressComponent']['province']
                city = result['regeocode']['addressComponent']['city']
                district = result['regeocode']['addressComponent']['district']
                adcode = result['regeocode']['addressComponent']['adcode']

                return {
                    'province': province,
                    'city': city,
                    'district': district,
                    'adcode': adcode
                }

    async def extract_to_location_detail(self, user_input: str, user_lat_lng: str):
        """
        从用户输入提取用户目的地的详细信息
        Args:
            user_input (str): 用户输入的原始文本
            user_lat_lng (str): 用户的经纬度坐标
        
        example:
        user_input: "这周六下午我想和女朋友在COCO Park约会"
        user_lag_lng: "22.520922,114.055198"
        return:
            "深圳市福田区COCO Park",
            ?? 目的地所在区？
            
        """
        user_location_info = await self.lat_lng2city(user_lat_lng)
        user_location_province = user_location_info['province']
        user_location_city = user_location_info['city']
        user_location_district = user_location_info['district']
        # user_location_adcode = user_location_info['adcode']

        user_location = user_location_province + user_location_city + user_location_district

        context = {
            'user_input': user_input,
            'user_location': user_location,
            'TODAY':TODAY,
            'WEEKDAY':WEEKDAY
    }

        # 构建完整的提示
        full_prompt = EXTRACT_TO_LOCATION_PROMPT.format(**context)


        # 调用 LLM（流式输出）
        stream = await async_client.chat.completions.create(
            model="qwen-plus",
            messages=[{"role": "system", "content": full_prompt}],
            stream=True,
        )

        full_content = ""
        async for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                content = chunk.choices[0].delta.content
                print(content, end="", flush=True)
                full_content += content
        
        # to_location_detail = full_content.replace('|', '')
        # to_location_mark = full_content
        to_location_lst = full_content.split('|')
        to_location_detail = to_location_lst[0]+to_location_lst[1]
        to_location_mark = to_location_lst[0] + '|' + to_location_lst[1]
        to_location_date = to_location_lst[2]
        
        
        

        print("\n")
        LOGGER.info('full_content')
        LOGGER.info(full_content)
        return {
            'to_location_detail': to_location_detail,
            'to_location_detail_mark': to_location_mark,
            'to_location_date': to_location_date
        }
        



    async def to_location2lat_lng(self, to_location_detail: str):
        """
        POI搜索-关键字搜索-目的地POI名称转换成目的地POI的经纬度坐标
        example.
        北京市海淀区北京大学->"116.310905,39.992806"
        Args:
            to_location_detail (str): POI名称，如"北京大学"、"上海外滩"等
        
        Returns:
            str: 经纬度坐标字符串，格式为"longitude,latitude"，如"116.310905,39.992806"
            None: 如果未找到匹配的POI
        """
        
        params = {
            'key': self.amap_api_key,
            'keywords': to_location_detail,
            'output': 'json',
            'extensions': 'all'  # 返回扩展信息
        }
        
        LOGGER.info(f"正在查询地点: {to_location_detail} 的经纬度坐标")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.keyword_search_url, params=params) as response:
                    response.raise_for_status()
                    result = await response.json()
                    LOGGER.info(result)
                    
                    # 检查是否有结果返回
                    if result.get('status') == '1' and result.get('pois') and len(result['pois']) > 0:
                        # 获取第一个POI的位置信息
                        location = result['pois'][0]['location']
                        province = result['pois'][0]['pname']
                        city = result['pois'][0]['cityname']

                        LOGGER.info(f"已找到地点 {to_location_detail} 的经纬度坐标: {location}")
                        return {
                            'province': province,
                            'city': city,
                            'location': location
                        }
                    else:
                        LOGGER.warning(f"未找到地点 {to_location_detail} 的经纬度坐标")
                        return None
        except Exception as e:
            LOGGER.error(f"查询地点 {to_location_detail} 的经纬度坐标时发生错误: {str(e)}")
            return None

    async def nearby_poi_search(self, to_location_lat_lng: str, poi_types: str, radius: int = 1000, offset: int = 20, page: int = 1) -> dict:
        """
        POI搜索-周边搜索-目的地POI经纬度周边POI的信息
        
        Args:
            to_location_lat_lng (str): 中心点经纬度，格式为"longitude,latitude"，如"116.472858,39.993882"
            radius (int, optional): 查询半径，单位米，取值范围:0-50000。默认为1000米。
            types (str, optional): POI类型，可以根据高德POI分类编码表进行设置。默认为None，表示不限制类型。
            offset (int, optional): 每页记录数量，默认为20，取值范围1-25
            page (int, optional): 当前页数，默认为1，取值范围1-100
            
        Returns:
            dict: 包含周边POI信息的结果字典
        """
        # 准备请求参数
        params = {
            'key': self.amap_api_key,
            'location': to_location_lat_lng,
            'radius': radius,
            'offset': offset,
            'page': page,
            'extensions': 'all',  # 返回详细信息
            'output': 'json',
            'types':poi_types
        }
        
        
        LOGGER.info(f"正在搜索经纬度 {to_location_lat_lng} 周边{radius}米范围内的POI")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.nearby_search_url, params=params) as response:
                    # 检查请求是否成功
                    response.raise_for_status()
                    
                    # 异步解析JSON响应
                    result = await response.json()
                    
                    # 检查是否有结果返回
                    if result.get('status') == '1':
                        count = int(result.get('count', 0))
                        LOGGER.info(f"成功找到 {count} 个POI")
                        
                        # 获取原始POI列表
                        original_pois = result.get('pois', [])
                        
                        # 只保留指定字段的POI信息
                        simplified_pois = []
                        for poi in original_pois:
                            # simplified_poi = {
                            #     'name': poi.get('name', ''),
                            #     'address': poi.get('address', ''),
                            #     'type': poi.get('type', ''),
                            #     'tel': poi.get('tel', ''),
                            #     'tag': poi.get('tag', []),
                            #     'website': poi.get('website', ''),
                            #     'distance': poi.get('distance', ''),
                            #     'location': poi.get('location', '')
                            # }
                            simplified_poi = {
                                    'name': poi['name'],
                                    'address': poi['address'],
                                    'type': poi['type'],
                                    'tel': poi['tel'],
                                    'tag': poi['tag'],
                                    'website': poi['website'],
                                    'distance': poi['distance'],
                                    'location': poi['location'],
                                    'photos': [i.get('url', '') for i in poi['photos']]
                                }
                            # 处理biz_ext中的cost字段
                            if 'biz_ext' in poi and 'cost' in poi['biz_ext']:
                                simplified_poi['cost'] = poi['biz_ext'].get('cost', [])
                            else:
                                simplified_poi['cost'] = []
                                
                            simplified_pois.append(simplified_poi)
                        
                        # 创建简化后的结果字典
                        simplified_result = {
                            'status': result.get('status', ''),
                            'info': result.get('info', ''),
                            'count': result.get('count', ''),
                            'pois': simplified_pois
                        }
                        
                        LOGGER.info("简化后的POI信息:")
                        LOGGER.info(simplified_result)
                        
                        return simplified_result
                    else:
                        LOGGER.warning(f"查询失败，状态码: {result.get('status')}, 信息: {result.get('info')}")
                        return result
        except Exception as e:
            LOGGER.error(f"搜索周边POI时发生错误: {str(e)}")
            return {
                'status': '0',
                'info': f'异步请求错误: {str(e)}',
                'pois': []
            }
    
    async def weather_search(self, to_location_adcode: str) -> dict:
        """
        查询指定城市编码和日期的天气信息
        
        Args:
            to_location_adcode: 城市编码(adcode)
            date: 日期，格式：'YYYY-MM-DD' 或 'base'（实时天气）
            
        Returns:
            dict: 天气信息
        """
        # 构建请求URL
        base_url = "https://restapi.amap.com/v3/weather/weatherInfo"
        params = {
            "key": self.amap_api_key,
            "city": to_location_adcode,
            "extensions": 'all',
            "output": 'JSON'
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(base_url, params=params) as response:
                    if response.status != 200:
                        return {'status': 'error', 'message': f'请求失败，状态码: {response.status}'}
                        
                    result = await response.json()
                    
                    # 处理API返回的错误
                    if result.get('status') != '1':
                        return {'status': 'error', 'message': result.get('info', '未知错误')}
                    return result['forecasts']
        except Exception as e:
            return {'status': 'error', 'message': f'请求异常: {str(e)}'}


    async def traffic_plan(self, origin: str, destination: str, mode: str = 'bus', city: str = '', city_d: str = '', 
                         strategy: int = 0, nightflag: int = 0, date: str = None, time: str = None) -> dict:
        """
        交通路径规划方法，支持多种交通方式
        
        Args:
            origin: 起点坐标，格式："经度,纬度"，如"116.481028,39.989643"
            destination: 终点坐标，格式："经度,纬度"，如"116.434446,39.90816"
            mode: 交通方式，可选值：'bus'(公交)、'walk'(步行)、'bike'(骑行)、'car'(驾车)，默认为'bus'
            city: 起点所在城市，公交、步行、骑行必填，驾车选填
            city_d: 终点所在城市，公交、步行、骑行必填，驾车选填
            strategy: 公交路径规划策略，仅公交模式有效：
                     0：最快捷模式
                     1：最经济模式
                     2：最少换乘模式
                     3：最少步行模式
                     5：不乘地铁模式
                     6：只坐地铁模式
                     7：时间短
            nightflag: 是否计算夜班车，0：不计算夜班车，1：计算夜班车，仅公交模式有效
            date: 出发日期，格式："YYYY-MM-DD"，公交模式可选
            time: 出发时间，格式："HH:MM"，公交模式可选
            


            
        Returns:
            dict: 路径规划结果
        """
        
        # 验证必填参数
        if not all([origin, destination]):
            return {'status': 'error', 'message': '起点和终点不能为空'}
            
        # if mode in ['bus', 'walk', 'bike'] and not all([city, city_d]):
        #     return {'status': 'error', 'message': f'{mode}模式需要指定起点和终点城市'}

        if mode == 'car':
            base_url = self.car_route_plan_url
        elif mode == 'bus':
            base_url = self.bus_route_plan_url
        
        # 构建请求参数
        params = {
            'key': self.amap_api_key,
            'origin': origin,
            'destination': destination,
            'output': 'JSON'
        }
        
        # 添加特定交通方式的参数
        if mode == 'bus':
            params.update({
                'city': city,
                'cityd': city_d,
                'strategy': strategy,
                'nightflag': nightflag
            })
            if date:
                params['date'] = date
            if time:
                params['time'] = time
        elif mode == 'car':
            # 驾车路线规划参数
            params.update({
                'origin': origin,
                'destination': destination,
                'strategy': 0,  # 0-速度最快，1-费用最少，2-距离最短，3-不走高速，4-高速优先
                'waypoints': '',  # 途经点，格式："经度,纬度;经度,纬度;..."
                'avoidpolygons': '',  # 避让区域，格式："经度,纬度;经度,纬度;..."
                'avoidroad': ''  # 避让道路，道路名称
            })
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(base_url, params=params) as response:
                    if response.status != 200:
                        return {'status': 'error', 'message': f'请求失败，状态码: {response.status}'}
                        
                    result = await response.json()
                    
                    # 处理API返回的错误
                    if result.get('status') != '1':
                        return {'status': 'error', 'message': result.get('info', '未知错误')}
                    
                    # 返回处理后的结果
                    if mode == 'bus':
                        return self._parse_bus_result(result)
                    elif mode == 'car':
                        return self._parse_drive_result(result)
                    
        except Exception as e:
            import traceback
            LOGGER.error(f"请求异常: {traceback.format_exc()}")
            return {'status': 'error', 'message': f'请求异常: {str(e)}'}
    
    def _parse_bus_result(self, result: dict) -> dict:
        """处理公交路径规划结果，提取导航指引信息"""
        try:
            route = result.get('route', {})
            if not route:
                return {'status': 'error', 'message': '未找到路线信息'}
            
            # 获取第一条路线
            transits = route.get('transits', [])
            if not transits:
                return {'status': 'error', 'message': '未找到公交路线'}
            
            # 选择第一条推荐路线
            transit_route = transits[0]
            
            # 提取基本信息
            cost = transit_route.get('cost', '0')
            walking_distance = int(transit_route.get('walking_distance', 0))
            distance = int(transit_route.get('distance', 0))
            duration = int(transit_route.get('duration', 0)) // 60  # 转换为分钟
            
            # 构建导航指引
            navigation_steps = []
            route_instructions = []
            
            # 处理每个路段
            for segment in transit_route.get('segments', []):
                # 处理步行段
                if segment.get('walking'):
                    walking = segment['walking']
                    walk_distance = int(walking.get('distance', 0))
                    walk_duration = int(walking.get('duration', 0)) // 60 if walking.get('duration') else 0
                    
                    # 获取步行指引
                    walk_instructions = []
                    for step in walking.get('steps', []):
                        instruction = step.get('instruction', '').replace('<b>', '').replace('</b>', '')
                        road = step.get('road', '')
                        action = step.get('action', '')
                        assistant_action = step.get('assistant_action', '')
                        
                        # 构建完整的步行指引
                        step_text = instruction
                        if road:
                            step_text = step_text.replace(f'沿{road}', f'沿<b>{road}</b>')
                        
                        walk_instructions.append(step_text)
                    
                    # 添加简化的步行段摘要
                    if walk_distance > 0:
                        summary = f'步行{walk_distance}米'
                        if walk_duration > 0:
                            summary += f'（{walk_duration}分钟）'
                        
                        # 添加目的地信息（如果有）- 但跳过经纬度格式的内容
                        destination = walking.get('destination', '')
                        if isinstance(destination, str) and destination and not (',' in destination and len(destination.split(',')) == 2):  # 跳过经纬度格式
                            summary += f'到达{destination}'
                        
                        route_instructions.append(summary)
                        
                        navigation_steps.append({
                            'type': 'walk',
                            'instruction': summary,
                            'distance': walk_distance,
                            'duration': walk_duration,
                            'details': walk_instructions
                        })
                
                # 处理公交/地铁段
                if segment.get('bus') and segment['bus'].get('buslines'):
                    bus = segment['bus']
                    buslines_list = bus.get('buslines', [])
                    
                    if not buslines_list:  # 处理空列表情况
                        continue
                        
                    buslines = buslines_list[0]  # 获取第一条线路
                    
                    # 获取基本信息
                    bus_name = buslines.get('name', '未知线路')
                    bus_type = buslines.get('type', '').replace('线路', '')
                    departure_stop = buslines.get('departure_stop', {}).get('name', '') if buslines.get('departure_stop') else ''
                    arrival_stop = buslines.get('arrival_stop', {}).get('name', '') if buslines.get('arrival_stop') else ''
                    
                    # 获取站点信息
                    via_stops = buslines.get('via_stops', [])
                    via_stops_names = [stop.get('name', '') for stop in via_stops if stop.get('name')]
                    via_num = int(buslines.get('via_num', 0)) or len(via_stops)
                    
                    # 处理方向信息
                    full_name = bus_name
                    direction = ''
                    
                    # 提取线路名称和方向
                    if '(' in bus_name and ')' in bus_name:
                        parts = bus_name.split('(', 1)
                        line_name = parts[0].strip()
                        direction_part = parts[1].split(')', 1)[0].strip()
                        
                        if '--' in direction_part:
                            direction_parts = direction_part.split('--')
                            direction = f'{direction_parts[0]}→{direction_parts[1]}'
                        else:
                            direction = direction_part
                    else:
                        line_name = bus_name
                    
                    # 获取入口和出口信息
                    entrance = bus.get('entrance', {})
                    entrance_name = ''
                    if entrance:
                        if isinstance(entrance, dict):
                            entrance_name = entrance.get('name', '')
                        elif isinstance(entrance, list) and entrance and isinstance(entrance[0], dict):
                            entrance_name = entrance[0].get('name', '')
                    
                    exit_info = bus.get('exit', {})
                    exit_name = ''
                    if exit_info:
                        if isinstance(exit_info, dict):
                            exit_name = exit_info.get('name', '')
                        elif isinstance(exit_info, list) and exit_info and isinstance(exit_info[0], dict):
                            exit_name = exit_info[0].get('name', '')
                    
                    # 计算时间和距离
                    bus_distance = int(buslines.get('distance', 0))
                    bus_duration = int(buslines.get('duration', 0)) // 60 if buslines.get('duration') else 0
                    
                    # 构建更简洁的指引信息
                    transport_type = '地铁' if '地铁' in bus_type else '公交'
                    
                    # 简化线路名称
                    if '地铁' in line_name:
                        display_name = line_name.replace('地铁', '').strip()
                    else:
                        display_name = line_name
                    
                    instruction = f'乘坐{display_name}'
                    
                    # 只保留起点站和终点站
                    instruction += f'，从{departure_stop}到{arrival_stop}'
                    
                    # 添加站点信息
                    if via_num > 0:
                        instruction += f'，{via_num}站'
                    
                    route_instructions.append(instruction)
                    
                    # 构建详细信息
                    details = [
                        f'• 交通工具: {transport_type} {line_name}',
                        f'• 方向: {direction}' if direction else '',
                        f'• 上车站: {departure_stop}',
                        f'• 下车站: {arrival_stop}',
                        f'• 途经站点: {via_num}站' + (f' ({"→".join(via_stops_names)})' if via_stops_names else ''),
                        f'• 预计用时: {bus_duration}分钟',
                        f'• 距离: {bus_distance}米'
                    ]
                    
                    # 过滤掉空字符串
                    details = [d for d in details if d]
                    
                    if entrance_name:
                        details.append(f'• 入口: {entrance_name}')
                    if exit_name:
                        details.append(f'• 出口: {exit_name}')
                    
                    navigation_steps.append({
                        'type': 'transit',
                        'transport_type': transport_type,
                        'line_name': line_name,
                        'instruction': instruction,
                        'departure_stop': departure_stop,
                        'arrival_stop': arrival_stop,
                        'via_stops': via_num,
                        'via_stops_names': via_stops_names,
                        'direction': direction,
                        'entrance': entrance_name,
                        'exit': exit_name,
                        'duration': bus_duration,
                        'distance': bus_distance,
                        'details': details
                    })
            
            # 简化返回结果，仅保留cost和instructions两个字段
            return {
                'status': 'success',
                'cost': cost,
                'instructions': route_instructions  # 简洁导航指引
            }
            
        except Exception as e:
            import traceback
            return {
                'status': 'error',
                'message': f'解析公交路线失败: {str(e)}',
                'traceback': traceback.format_exc()
            }
    
    def _parse_drive_result(self, result: dict) -> dict:
        """处理驾车路径规划结果，提供简洁的导航指引"""
        try:
            path = result.get('route', {}).get('paths', [{}])[0]
            
            # 提取基本信息
            distance = int(path.get('distance', 0))  # 总距离，米
            duration = int(path.get('duration', 0)) // 60  # 预计耗时，分钟
            tolls = path.get('tolls', '0')  # 过路费
            traffic_lights = int(path.get('traffic_lights', 0))  # 红绿灯个数
            
            # 构建简洁的导航指引
            route_instructions = []
            
            # 添加总体信息
            route_instructions.append(f'驾车{distance}米（{duration}分钟），经过{traffic_lights}个红绿灯')
            
            # 提取所有驾驶步骤
            all_steps = path.get('steps', [])
            
            # 处理每个导航指令
            for i, step in enumerate(all_steps):
                instruction = step.get('instruction', '').replace('<b>', '').replace('</b>', '')
                road = step.get('road', '')
                distance = int(step.get('distance', 0))
                orientation = step.get('orientation', '')
                
                # 跳过距离很短的步骤（小于50米且没有道路名称）
                if distance < 50 and not road:
                    # 但如果是最后一步，则保留
                    if i == len(all_steps) - 1:
                        route_instructions.append(f'{instruction}')
                    continue
                
                # 判断是否是重要路段
                is_important = distance > 300 or (road and distance > 100) or '隐道' in instruction or '高速' in instruction
                
                if is_important:
                    # 构建更信息丰富的导航指令
                    clean_instruction = instruction
                    
                    # 如果原始指令包含“途径”信息但不包含方向，添加方向
                    if '途径' in clean_instruction and orientation and '行驶' in clean_instruction:
                        direction_added = False
                        for direction in ['向东', '向西', '向南', '向北', '向东南', '向东北', '向西南', '向西北']:
                            if direction in clean_instruction:
                                direction_added = True
                                break
                                
                        if not direction_added:
                            clean_instruction = clean_instruction.replace('行驶', f'向{orientation}行驶')
                    
                    # 简化特殊指令的表达方式
                    if '进入隐道' in clean_instruction:
                        clean_instruction = clean_instruction.replace('直行进入隐道', '进入隐道')
                    if '高速' in clean_instruction and '出口' in clean_instruction:
                        clean_instruction = f'从高速公路出口驾驶{distance}米'
                    
                    # 稍微美化一下文本
                    clean_instruction = clean_instruction.replace('米向', '米，向')
                    
                    route_instructions.append(clean_instruction)
                
            # 如果指令太多，只保留前10条
            if len(route_instructions) > 11:  # 包括前面的总体信息，所以是11
                # 保留第一条（总体信息）和重要的几条指令
                route_instructions = [route_instructions[0]] + route_instructions[1:10]
            
            # 确保最后一条是到达目的地的提示
            if all_steps and '到达目的地' in all_steps[-1].get('instruction', ''):
                if len(route_instructions) > 1 and '到达目的地' not in route_instructions[-1]:
                    route_instructions.append(f'{all_steps[-1].get("instruction", "")}')
            
            # 简化返回结果
            return {
                'status': 'success',
                'cost': tolls,  # 与公交模式保持一致的字段名
                'instructions': route_instructions
            }
            
        except Exception as e:
            import traceback
            return {
                'status': 'error',
                'message': f'解析驾车路线失败: {str(e)}',
                'traceback': traceback.format_exc()
            }

    # async def to_location_poi_search(self, user_lag_lng: str, user_input: str) -> dict:

    #     """
    #     POI搜索-关键字搜索-目的地POI名称转换成目的地POI的经纬度坐标
    #     example.
    #     """
    #     to_location_detail = await self.extract_to_location_detail(
    #         user_input=user_input, user_lag_lng=user_lag_lng)
    #     to_location_lag_lng = await self.to_location2lag_lng(to_location_detail)
    #     to_location_food_task = self.nearby_poi_search(
    #         to_location_lag_lng, poi_types='050000', radius=600, offset=15)
    #     to_location_shopping_task = self.nearby_poi_search(
    #         to_location_lag_lng, poi_types='060100', radius=700, offset=3)
    #     to_location_entertainment_task = self.nearby_poi_search(
    #         to_location_lag_lng, poi_types='080000', radius=1000, offset=15)
    #     to_location_sightseeing_task = self.nearby_poi_search(
    #         to_location_lag_lng, poi_types='110000', radius=1500, offset=10)
    #     results = await asyncio.gather(
    #         to_location_food_task,
    #         to_location_shopping_task,
    #         to_location_entertainment_task,
    #         to_location_sightseeing_task
    #     )
    #     to_location_food = results[0]['pois']
    #     to_location_shopping = results[1]['pois']
    #     to_location_entertainment = results[2]['pois']
    #     to_location_sightseeing = results[3]['pois']
    #     return {    
    #         'to_location_food': to_location_food,
    #         'to_location_shopping': to_location_shopping,
    #         'to_location_entertainment': to_location_entertainment,
    #         'to_location_sightseeing': to_location_sightseeing,
    #     }


if __name__ == "__main__":
    location_helper = LocationHelper()
    
    # 创建事件循环并运行异步函数
    # async def main():
    #     result = await location_helper.lag_lng2city('113.999706,22.588863')
    #     print(result)
    
    # asyncio.run(main())
    # asyncio.run(location_helper.extract_to_location_detail('下周五早上我想和去朋友去宽窄巷子约会', '113.999706,22.588863'))
    asyncio.run(location_helper.to_location2lat_lng('广东省深圳市怡乐花园'))
    # asyncio.run(location_helper.nearby_poi_search(to_location_lat_lng='104.066821,30.679801', poi_types='110000'))
    # res = asyncio.run(location_helper.weather_search(to_location_adcode='440305', extensions='all'))
    # print(res)
    # print(asyncio.run(location_helper.traffic_plan(origin='113.999706,22.588863', destination='114.055198,22.520922', mode='car', city='深圳', city_d='深圳')))
    
    
