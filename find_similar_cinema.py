#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File	:find_similar_cinema.py
@Time	:2025/05/13 11:18:48
<AUTHOR>
@Mail	:<EMAIL>
'''

import json
import asyncio
from typing import List, Dict, Any, Optional
from embedding import TextEmbeddingService
from logger import LOGGER


class CinemaSimilarityFinder:
    """
    影院相似度查找器
    """

    def __init__(self, embedding_service: TextEmbeddingService = None):
        """
        初始化影院相似度查找器

        Args:
            embedding_service: 文本向量嵌入服务，如果为None则创建默认服务
        """
        self.embedding_service = embedding_service or TextEmbeddingService()

    @staticmethod
    def create(api_key: str = None) -> "CinemaSimilarityFinder":
        """
        工厂方法，创建CinemaSimilarityFinder实例

        Args:
            api_key: API密钥，如果为None则使用环境变量

        Returns:
            CinemaSimilarityFinder: 新创建的查找器实例
        """
        embedding_service = TextEmbeddingService(api_key=api_key)
        return CinemaSimilarityFinder(embedding_service)

    async def find_most_similar(
        self,
        cinemas: List[Dict[str, Any]],
        query: str = "coco park",
        fields: List[str] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        异步方式找出影院列表中与查询词最相似的影院

        Args:
            cinemas: 影院列表
            query: 查询词
            fields: 要返回的字段列表，默认为['cinemaId', 'title', 'location']

        Returns:
            Dict: 最相似的影院信息，包含指定字段
        """
        if not cinemas:
            return None

        if fields is None:
            fields = ["cinemaId", "title", "location"]

        # 获取查询词的embedding
        query_embedding, _ = await self.embedding_service.get_embedding(query)
        if not query_embedding:
            return None

        # 准备所有影院标题
        titles = [cinema["title"] for cinema in cinemas]

        # 异步获取所有标题的embedding
        tasks = []
        for title in titles:
            task = self.embedding_service.get_embedding(title)
            tasks.append(task)

        # 等待所有embedding计算完成
        embeddings_results = await asyncio.gather(*tasks)

        # 计算相似度并找出最相似的影院
        max_similarity = -1
        most_similar_cinema = None
        similarities = []

        for i, (title_embedding, title) in enumerate(embeddings_results):
            if title_embedding:
                cinema = cinemas[i]
                similarity = self.embedding_service.cosine_similarity(
                    query_embedding, title_embedding
                )
                similarities.append((cinema, similarity))

                if similarity > max_similarity:
                    max_similarity = similarity
                    most_similar_cinema = cinema

        if not most_similar_cinema:
            return None

        # 打印所有结果以便比较
        LOGGER.info("所有影院相似度结果:")
        for cinema, similarity in similarities:
            LOGGER.info(f"{cinema['title']}: {similarity:.4f}")

        LOGGER.info(
            f"\n最相似的影院: {most_similar_cinema['title']} (相似度: {max_similarity:.4f})"
        )

        # 只返回指定字段的结果
        result = {}
        for field in fields:
            result[field] = most_similar_cinema.get(field)
        LOGGER.info(f'result: {result}')
        return result

    def find_most_similar_sync(
        self,
        cinemas: List[Dict[str, Any]],
        query: str = "coco park",
        fields: List[str] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        同步方式找出影院列表中与查询词最相似的影院

        Args:
            cinemas: 影院列表
            query: 查询词
            fields: 要返回的字段列表，默认为['cinemaId', 'title', 'location']

        Returns:
            Dict: 最相似的影院信息，包含指定字段
        """
        return asyncio.run(self.find_most_similar(cinemas, query, fields))


# 向下兼容的旧函数
# 使用新的CinemaSimilarityFinder类实现
def find_most_similar_cinema(cinemas, query="coco park"):
    """
    同步版本的找出影院列表中与查询词最相似的影院函数（用于向下兼容）
    """
    finder = CinemaSimilarityFinder()
    return finder.find_most_similar_sync(cinemas, query)


async def main():
    """
    示例代码：演示如何使用CinemaSimilarityFinder类
    """
    # 示例影院列表
    cinemas_json = """
    [
        {"cinemaId": 12849, "title": "纵横国际影城（石厦店）", "price": {"n": "43.9", "q": "元起"}, "location": "福田区石厦北二街89号石厦时代广场3层", "distance": "200m", "services": [{"text": "退", "code": "allowRefund"}, {"text": "改签", "code": "endorse"}, {"text": "小吃", "code": "snack"}, {"text": "折扣卡", "code": "vipTag"}], "discount": []}, 
        {"cinemaId": 37406, "title": "九色国际影城（水围店）", "price": {"n": "29.9", "q": "元起"}, "location": "福田区金田路银庄大厦3层301（福民地铁D出口）", "distance": "1km", "services": [{"text": "退", "code": "allowRefund"}, {"text": "改签", "code": "endorse"}, {"text": "小吃", "code": "snack"}, {"text": "折扣卡", "code": "vipTag"}, {"text": "杜比全景声厅", "code": "hallType"}], "discount": []}, 
        {"cinemaId": 30840, "title": "英皇电影城(COCO park店)", "price": {"n": "45", "q": "元起"}, "location": "福田区益田路5023号平安金融中心商场南塔L4层", "distance": "1.4km", "services": [{"text": "小吃", "code": "snack"}, {"text": "IMAX厅", "code": "hallType"}], "discount": []}
    ]
    """

    import time

    start_time = time.time()

    cinemas = json.loads(cinemas_json)

    # 创建类的两种方式
    # 方式1：直接创建
    finder1 = CinemaSimilarityFinder()
    # 方式2：通过工厂方法创建，可指定API Key
    # finder2 = CinemaSimilarityFinder.create(api_key="your_api_key")

    # 异步使用
    most_similar = await finder1.find_most_similar(
        cinemas, "coco park", ["cinemaId", "title", "location"]
    )

    end_time = time.time()
    print(f"异步处理总耗时: {end_time - start_time:.2f}秒")

    # 显示结果中的字段
    print("\n返回的影院信息:")
    print(f"  cinemaId: {most_similar.get('cinemaId')}")
    print(f"  title: {most_similar.get('title')}")
    print(f"  location: {most_similar.get('location')}")


def show_sync_usage_example():
    """
    示例代码：演示如何使用同步方式
    """
    cinemas_json = """
    [
        {"cinemaId": 12849, "title": "纵横国际影城（石厦店）", "price": {"n": "43.9", "q": "元起"}, "location": "福田区石厦北二街89号石厦时代广场3层", "distance": "200m"}, 
        {"cinemaId": 37406, "title": "九色国际影城（水围店）", "price": {"n": "29.9", "q": "元起"}, "location": "福田区金田路银庄大厦3层301（福民地铁D出口）", "distance": "1km"}, 
        {"cinemaId": 30840, "title": "英皇电影城(COCO park店)", "price": {"n": "45", "q": "元起"}, "location": "福田区益田路5023号平安金融中心商场南塔L4层", "distance": "1.4km"}
    ]
    """

    cinemas = json.loads(cinemas_json)

    # 创建影院相似度查找器
    finder = CinemaSimilarityFinder()

    # 同步方式使用
    most_similar = finder.find_most_similar_sync(cinemas, "coco park")
    print(f"找到的最相似影院: {most_similar}")
    print(f"影院名称: {most_similar.get('title')}")
    print(f"影院位置: {most_similar.get('location')}")


if __name__ == "__main__":
    # 异步示例
    asyncio.run(main())

    # 如果需要被导入为模块使用，这里可以演示同步使用方式
    # show_sync_usage_example()
