#!/usr/bin/env python
# -*- coding: utf-8 -*-

with open('/Users/<USER>/work/202505/dating-agent-http/dating-agent/api.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 替换错误处理部分
new_content = content.replace(
    'except Exception as e:\n            yield {"data": f"错误: {str(e)}"}', 
    'except Exception as e:\n            yield ServerSentEvent(data=f"错误: {str(e)}", event="error")'
)

# 同时修改其他API端点的错误处理
new_content = new_content.replace(
    'except Exception as e:\n            yield {"data": f"错误: {str(e)}"}',
    'except Exception as e:\n            yield ServerSentEvent(data=f"错误: {str(e)}", event="error")'
)

with open('/Users/<USER>/work/202505/dating-agent-http/dating-agent/api.py', 'w', encoding='utf-8') as f:
    f.write(new_content)

print('API文件修复完成！')
