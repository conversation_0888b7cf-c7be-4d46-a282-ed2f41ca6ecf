#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File	:dating_consult_bot.py
@Time	:2025/05/15 15:41:35
<AUTHOR>
@Mail	:<EMAIL>
'''

from langchain_core.messages import SystemMessage, HumanMessage
from llm import chat_openai_client

class DatingConsultBot:
    """
    约会技巧咨询机器人
    为用户提供约会技巧、建议和指导
    """
    
    def __init__(self):
        self.llm = chat_openai_client
        self.system_prompt = """
        你是一位专业的约会技巧顾问，擅长提供实用的约会建议和技巧。
        你的职责是帮助用户解决约会过程中的问题、提升约会技巧、增强个人魅力。
        
        请提供专业、实用且符合当前社会价值观的建议，尊重每个人的个性和选择。
        回答应该友好、专业，并且基于心理学和人际关系的科学知识。
        
        当用户咨询约会计划或安排具体约会活动时，请告知用户你只提供约会技巧咨询，
        如果他们需要具体的约会计划，需要使用约会规划服务。
        """
    
    async def respond(self, user_message: str) -> str:
        """
        处理用户消息并返回响应
        
        Args:
            user_message: 用户输入的消息
            
        Returns:
            机器人的回复
        """
        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=user_message)
        ]
        
        response = await self.llm.ainvoke(messages)
        
        if hasattr(response, 'content'):
            return response.content
        else:
            return str(response)
