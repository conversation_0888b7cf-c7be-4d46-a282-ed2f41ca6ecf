<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>u7ea6u4f1au89c4u5212APIu5ba2u6237u7aef (u7b80u5316u7248)</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1, h2 {
            color: #333;
        }
        .container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .panel {
            flex: 1;
            min-width: 300px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px;
            margin-bottom: 20px;
        }
        .panel h2 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        .output {
            margin-top: 15px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .status {
            color: #777;
            font-style: italic;
            margin-top: 10px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #45a049;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .hidden {
            display: none;
        }
        .category {
            font-weight: bold;
            margin-top: 10px;
            color: #0066cc;
        }
        .item {
            margin-left: 15px;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>u7ea6u4f1au89c4u5212APIu5ba2u6237u7aef (u7b80u5316u7248)</h1>
    <p>u8fd9u4e2au5ba2u6237u7aefu6f14u793au4e86u5982u4f55u8fdeu63a5u548cu4f7fu7528u7ea6u4f1au89c4u5212APIu7684u7b80u5316u7248u7aefu70b9uff0cu4f7fu7528u666eu901aJSONu54cdu5e94u6765u907fu514dSSEu95eeu9898</p>
    
    <div class="container">
        <div class="panel">
            <h2>u89e3u91cau7528u6237u7ea6u4f1au671fu671b</h2>
            <button id="explainBtn">u83b7u53d6u7ea6u4f1au671fu671bu89e3u91ca</button>
            <div class="status" id="explainStatus"></div>
            <div class="loading hidden" id="explainLoading"></div>
            <div class="output" id="explainOutput"></div>
        </div>
        
        <div class="panel">
            <h2>u67e5u8be2u76eeu7684u5730u4fe1u606f</h2>
            <button id="searchLocationBtn">u83b7u53d6u76eeu7684u5730u4fe1u606f</button>
            <div class="status" id="searchLocationStatus"></div>
            <div class="loading hidden" id="searchLocationLoading"></div>
            <div class="output" id="searchLocationOutput"></div>
        </div>
    </div>

    <script>
        // u793au4f8bu8bf7u6c42u6570u636e
        const requestData = {
            user_input: "u8fd9u5468u516du4e0bu5348u6211u60f3u548cu5973u670bu53cbu5728COCO Parku7ea6u4f1a",
            request_type: "dating",
            dating_params: {
                to_location_province: "u5e7fu4e1cu7701",
                to_location_city: "u6df1u5733u5e02",
                to_location_district: "u798fu7530u533a",
                to_location_name: "COCO Park",
                
                user_lat_lng: "113.999706,22.588863",
                user_location_province: "u5e7fu4e1cu7701",
                user_location_city: "u6df1u5733u5e02",
                user_location_district: "u5357u5c71u533a",
                user_location_name: "u5858u6717u57ceu5e7fu573a",
                
                departure_lat_lng: "113.999706,22.588863",
                departure_province: "u5e7fu4e1cu7701",
                departure_city: "u6df1u5733u5e02",
                departure_district: "u5357u5c71u533a",
                departure_name: "u5858u6717u57ceu5e7fu573a",
                
                comp_lat_lng: "114.054007,22.533569",
                comp_location_province: "u5e7fu4e1cu7701",
                comp_location_city: "u6df1u5733u5e02",
                comp_location_district: "u5b9du5b89u533a",
                comp_location_name: "u5c1au90fdu82b1u56ed",
                
                male_last_name: "u5f20",
                female_last_name: "u674e",
                
                trans_tool_user: "bus",
                trans_tool_comp: "bus",
                
                date: "2025-05-24",
                time_start: "14:00",
                time_end: "17:00",
                
                dating_times: 1,
                dating_type: "u6d6au6f2bu578b",
                more_thoughts: "u6211u5e0cu671bu7ea6u4f1au65f6u53efu4ee5u5403u5230u51b0u6dc7u6dcb"
            }
        };

        const API_BASE_URL = 'http://localhost:8001';
        
        // u8f85u52a9u51fdu6570uff1au521bu5efau6b63u5e38u7684JSONu8bf7u6c42
        function fetchData(endpoint, outputElement, statusElement, loadingElement, processCallback) {
            // u91cdu7f6eu8f93u51fa
            outputElement.textContent = '';
            statusElement.textContent = 'u6b63u5728u8bf7u6c42u6570u636e...';
            loadingElement.classList.remove('hidden');
            
            // u521bu5efau8bf7u6c42u9009u9879
            const requestOptions = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            };
            
            // u4f7fu7528fetchu8fdbu884cu8bf7u6c42
            fetch(`${API_BASE_URL}${endpoint}`, requestOptions)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP u9519u8bef! u72b6u6001: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    loadingElement.classList.add('hidden');
                    statusElement.textContent = 'u6570u636eu63a5u6536u5b8cu6210';
                    processCallback(data, outputElement);
                })
                .catch(error => {
                    loadingElement.classList.add('hidden');
                    statusElement.textContent = `u9519u8bef: ${error.message}`;
                    console.error('u8bf7u6c42u9519u8bef:', error);
                });
        }
        
        // u521du59cbu5316u6240u6709u6309u94ae
        document.addEventListener('DOMContentLoaded', () => {
            // u89e3u91cau7528u6237u7ea6u4f1au671fu671b
            document.getElementById('explainBtn').addEventListener('click', () => {
                const outputElement = document.getElementById('explainOutput');
                const statusElement = document.getElementById('explainStatus');
                const loadingElement = document.getElementById('explainLoading');
                
                fetchData('/explain', outputElement, statusElement, loadingElement, 
                    (data, outputElement) => {
                        outputElement.innerHTML = `<div><strong>u7ea6u4f1au671fu671bu89e3u91ca:</strong></div><div>${data.result}</div>`;
                    });
            });
            
            // u67e5u8be2u76eeu7684u5730u4fe1u606f
            document.getElementById('searchLocationBtn').addEventListener('click', () => {
                const outputElement = document.getElementById('searchLocationOutput');
                const statusElement = document.getElementById('searchLocationStatus');
                const loadingElement = document.getElementById('searchLocationLoading');
                
                fetchData('/search-location', outputElement, statusElement, loadingElement,
                    (data, outputElement) => {
                        // u6e05u7a7au539fu6709u5185u5bb9
                        outputElement.innerHTML = '';
                        
                        // u6dfbu52a0u6570u636eu6458u8981
                        const summary = document.createElement('div');
                        summary.innerHTML = `<strong>u5df2u627eu5230 ${data.count} u4e2au5730u70b9</strong>`;
                        outputElement.appendChild(summary);
                        
                        // u6dfbu52a0u9910u996eu573au6240
                        if (data.to_location_food && data.to_location_food.length > 0) {
                            const category = document.createElement('div');
                            category.className = 'category';
                            category.textContent = `u9910u996eu573au6240 (${data.to_location_food.length}):`;
                            outputElement.appendChild(category);
                            
                            data.to_location_food.forEach(item => {
                                const itemDiv = document.createElement('div');
                                itemDiv.className = 'item';
                                itemDiv.textContent = item.name;
                                outputElement.appendChild(itemDiv);
                            });
                        }
                        
                        // u6dfbu52a0u8d2du7269u573au6240
                        if (data.to_location_shopping && data.to_location_shopping.length > 0) {
                            const category = document.createElement('div');
                            category.className = 'category';
                            category.textContent = `u8d2du7269u573au6240 (${data.to_location_shopping.length}):`;
                            outputElement.appendChild(category);
                            
                            data.to_location_shopping.forEach(item => {
                                const itemDiv = document.createElement('div');
                                itemDiv.className = 'item';
                                itemDiv.textContent = item.name;
                                outputElement.appendChild(itemDiv);
                            });
                        }
                        
                        // u6dfbu52a0u5a31u4e50u573au6240
                        if (data.to_location_entertainment && data.to_location_entertainment.length > 0) {
                            const category = document.createElement('div');
                            category.className = 'category';
                            category.textContent = `u5a31u4e50u573au6240 (${data.to_location_entertainment.length}):`;
                            outputElement.appendChild(category);
                            
                            data.to_location_entertainment.forEach(item => {
                                const itemDiv = document.createElement('div');
                                itemDiv.className = 'item';
                                itemDiv.textContent = item.name;
                                outputElement.appendChild(itemDiv);
                            });
                        }
                        
                        // u6dfbu52a0u666fu70b9
                        if (data.to_location_sightseeing && data.to_location_sightseeing.length > 0) {
                            const category = document.createElement('div');
                            category.className = 'category';
                            category.textContent = `u666fu70b9 (${data.to_location_sightseeing.length}):`;
                            outputElement.appendChild(category);
                            
                            data.to_location_sightseeing.forEach(item => {
                                const itemDiv = document.createElement('div');
                                itemDiv.className = 'item';
                                itemDiv.textContent = item.name;
                                outputElement.appendChild(itemDiv);
                            });
                        }
                    });
            });
        });
    </script>
</body>
</html>
