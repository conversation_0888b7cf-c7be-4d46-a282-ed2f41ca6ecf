#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File	:api.py
@Time	:2025/05/26 18:03:33
<AUTHOR>
@Mail	:<EMAIL>
'''

import asyncio
from typing import Optional

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from sse_starlette.sse import EventSourceResponse, ServerSentEvent
from pydantic import BaseModel, Field
import uvicorn

from main import (
    explain_request,
    think_plan,
    search_purpose_location_info,
    get_detailed_plan
)

# 创建FastAPI应用
app = FastAPI(
    title="约会规划API",
    description="提供约会规划相关的API接口，使用SSE方式传输数据",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 定义请求模型
class DatingParams(BaseModel):
    to_location_province: str = Field(description="目的地省份", example="广东省")
    to_location_city: str = Field(description="目的地城市", example="深圳市")
    to_location_district: str = Field(description="目的地区县", example="福田区")
    to_location_name: str = Field(description="目的地名称", example="COCO Park")
    
    user_lat_lng: str = Field(description="用户位置经纬度", example="113.999706,22.588863")
    user_location_province: str = Field(description="用户位置省份", example="广东省")
    user_location_city: str = Field(description="用户位置城市", example="深圳市")
    user_location_district: str = Field(description="用户位置区县", example="南山区")
    user_location_name: str = Field(description="用户位置名称", example="塘朗城广场")
    
    departure_lat_lng: str = Field(description="出发地经纬度", example="113.999706,22.588863")
    departure_province: str = Field(description="出发地省份", example="广东省")
    departure_city: str = Field(description="出发地城市", example="深圳市")
    departure_district: str = Field(description="出发地区县", example="南山区")
    departure_name: str = Field(description="出发地名称", example="塘朗城广场")
    
    comp_lat_lng: str = Field(description="伴侣位置经纬度", example="114.054007,22.533569")
    comp_location_province: str = Field(description="伴侣位置省份", example="广东省")
    comp_location_city: str = Field(description="伴侣位置城市", example="深圳市")
    comp_location_district: str = Field(description="伴侣位置区县", example="宝安区")
    comp_location_name: str = Field(description="伴侣位置名称", example="尚都花园")
    
    male_last_name: str = Field(description="男方姓氏", example="张")
    female_last_name: str = Field(description="女方姓氏", example="李")
    
    trans_tool_user: str = Field(description="用户交通方式 bus-公交 car-自驾", example="bus")
    trans_tool_comp: str = Field(description="伴侣交通方式 bus-公交 car-自驾", example="bus")
    
    date: str = Field(description="约会日期", example="2025-05-24")
    time_start: str = Field(description="约会开始时间", example="14:00")
    time_end: str = Field(description="约会结束时间", example="17:00")
    
    dating_times: int = Field(description="约会次数 1-1次 2-2次 3-3多次 4-确认关系", example=1)
    dating_type: str = Field(description="约会偏好 浪漫型/趣味型/文艺型", example="浪漫型")
    more_thoughts: Optional[str] = Field(default=None, description="更多想法", example="我希望约会时可以吃到冰淇淋")

class RequestModel(BaseModel):
    user_input: str = Field(description="用户输入", example="这周六下午我想和女朋友在COCO Park约会")
    request_type: str = Field(description="请求类型 'dating':约会规划，'chat':对话聊天", example="dating")
    dating_params: DatingParams

# API路由
@app.get("/")
async def root():
    return {"message": "约会规划API服务已启动"}

@app.post("/explain/stream")
async def api_explain_request(request_data: RequestModel):
    """解释用户约会期望（SSE方式）"""
    params = request_data.model_dump()
    
    async def event_generator():
        try:
            result = explain_request(params)
            # 将结果分段发送
            yield ServerSentEvent(data="开始解析用户约会期望...", event="start")
            await asyncio.sleep(0.2)  # 短暂延迟
            yield ServerSentEvent(data=result, event="result")
            yield ServerSentEvent(data="约会期望解析完成", event="end")
        except Exception as e:
            yield ServerSentEvent(data=f"错误: {str(e)}", event="error")
            
    return EventSourceResponse(event_generator())

@app.post("/think-plan/stream")
async def api_think_plan(request_data: RequestModel):
    """用户需求理解和任务拆分（SSE方式）"""
    params = request_data.model_dump()
    
    async def event_generator():
        try:
            async for content in think_plan(params):
                if content:
                    yield ServerSentEvent(data=content, event="message")
        except Exception as e:
            yield ServerSentEvent(data=f"错误: {str(e)}", event="error")
            
    return EventSourceResponse(event_generator())

@app.post("/search-location/stream")
async def api_search_location(request_data: RequestModel):
    """查询目的地信息（SSE方式）"""
    params = request_data.model_dump()
    
    async def event_generator():
        try:
            result = await search_purpose_location_info(params)
            # 由于这个函数不是生成器，我们需要手动将结果转换为SSE流
            # 分批次发送不同类型的POI信息
            yield ServerSentEvent(data="开始获取目的地信息...", event="start")
            
            # 发送美食信息
            yield ServerSentEvent(data=f"找到{len(result['to_location_food'])}个餐饮场所", event="info")
            for item in result['to_location_food']:
                # 使用ServerSentEvent对象发送数据
                name = item.get('name', '')
                yield ServerSentEvent(data=name, event="food")
                await asyncio.sleep(0.1)  # 稍微延迟，避免数据发送过快
            
            # 发送购物信息
            yield ServerSentEvent(data=f"找到{len(result['to_location_shopping'])}个购物场所", event="info")
            for item in result['to_location_shopping']:
                name = item.get('name', '')
                yield ServerSentEvent(data=name, event="shopping")
                await asyncio.sleep(0.1)
            
            # 发送娱乐信息
            yield ServerSentEvent(data=f"找到{len(result['to_location_entertainment'])}个娱乐场所", event="info")
            for item in result['to_location_entertainment']:
                name = item.get('name', '')
                yield ServerSentEvent(data=name, event="entertainment")
                await asyncio.sleep(0.1)
            
            # 发送景点信息
            yield ServerSentEvent(data=f"找到{len(result['to_location_sightseeing'])}个景点", event="info")
            for item in result['to_location_sightseeing']:
                name = item.get('name', '')
                yield ServerSentEvent(data=name, event="sightseeing")
                await asyncio.sleep(0.1)
            
            yield ServerSentEvent(data=f"总计找到{result['count']}个地点", event="summary")
            yield ServerSentEvent(data="目的地信息获取完成", event="end")
            
        except Exception as e:
            yield ServerSentEvent(data=f"错误: {str(e)}", event="error")
            
    return EventSourceResponse(event_generator())

@app.post("/detailed-plan/stream")
async def api_detailed_plan(request_data: RequestModel):
    """获取详细约会计划（SSE方式）"""
    params = request_data.model_dump()
    
    async def event_generator():
        try:
            async for content in get_detailed_plan(params):
                if content:
                    yield ServerSentEvent(data=content, event="message")
        except Exception as e:
            yield ServerSentEvent(data=f"错误: {str(e)}", event="error")
            
    return EventSourceResponse(event_generator())

# 主入口
if __name__ == "__main__":
    uvicorn.run("api:app", host="0.0.0.0", port=8000, reload=True)
